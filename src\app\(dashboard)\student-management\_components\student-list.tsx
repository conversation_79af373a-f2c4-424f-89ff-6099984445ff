// src/components/student-management/student-list.tsx
'use client';

import { useState } from 'react';
import { ComponentErrorBoundary } from '../../../../components/common/page-error-boundary';

interface Student {
  id: string;
  name: string;
  class: string;
  section: string;
  feesStatus: 'Paid' | 'Pending';
  extracurricular: string;
}

interface StudentListProps {
  onAddNewStudent: () => void;
  onExport: () => void;
}

const StudentList = ({ onAddNewStudent, onExport }: StudentListProps) => {
  const [classFilter, setClassFilter] = useState('All Classes');
  const [sectionFilter, setSectionFilter] = useState('All Sections');
  const [extracurricularFilter, setExtracurricularFilter] = useState('Any');
  const [feesStatusFilter, setFeesStatusFilter] = useState('Any');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Student data - will be populated from actual service
  const [students] = useState<Student[]>([
    // Sample data for pagination demonstration
    { id: 'STU001', name: 'Alice Johnson', class: 'Class 9', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Arts Club' },
    { id: 'STU002', name: 'Bob Smith', class: 'Class 10', section: 'Section B', feesStatus: 'Pending', extracurricular: 'Football Team' },
    { id: 'STU003', name: 'Carol Davis', class: 'Class 9', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Music Band' },
    { id: 'STU004', name: 'David Wilson', class: 'Class 11', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Debate Club' },
    { id: 'STU005', name: 'Emma Brown', class: 'Class 10', section: 'Section A', feesStatus: 'Pending', extracurricular: 'Science Club' },
    { id: 'STU006', name: 'Frank Miller', class: 'Class 12', section: 'Section B', feesStatus: 'Paid', extracurricular: 'Arts Club' },
    { id: 'STU007', name: 'Grace Lee', class: 'Class 9', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Football Team' },
    { id: 'STU008', name: 'Henry Taylor', class: 'Class 11', section: 'Section A', feesStatus: 'Pending', extracurricular: 'Music Band' },
    { id: 'STU009', name: 'Isabella Garcia', class: 'Class 10', section: 'Section B', feesStatus: 'Paid', extracurricular: 'Debate Club' },
    { id: 'STU010', name: 'Jack Anderson', class: 'Class 12', section: 'Section C', feesStatus: 'Pending', extracurricular: 'Science Club' },
    { id: 'STU011', name: 'Karen White', class: 'Class 9', section: 'Section B', feesStatus: 'Paid', extracurricular: 'Arts Club' },
    { id: 'STU012', name: 'Lucas Martinez', class: 'Class 11', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Football Team' },
    { id: 'STU013', name: 'Mia Rodriguez', class: 'Class 10', section: 'Section C', feesStatus: 'Pending', extracurricular: 'Music Band' },
    { id: 'STU014', name: 'Noah Thompson', class: 'Class 12', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Debate Club' },
    { id: 'STU015', name: 'Olivia Moore', class: 'Class 9', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Science Club' },
    { id: 'STU016', name: 'Paul Jackson', class: 'Class 11', section: 'Section B', feesStatus: 'Pending', extracurricular: 'Arts Club' },
    { id: 'STU017', name: 'Quinn Harris', class: 'Class 10', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Football Team' },
    { id: 'STU018', name: 'Rachel Clark', class: 'Class 12', section: 'Section B', feesStatus: 'Paid', extracurricular: 'Music Band' },
    { id: 'STU019', name: 'Samuel Lewis', class: 'Class 9', section: 'Section A', feesStatus: 'Pending', extracurricular: 'Debate Club' },
    { id: 'STU020', name: 'Tara Walker', class: 'Class 11', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Science Club' },
    { id: 'STU021', name: 'Uriel Hall', class: 'Class 10', section: 'Section B', feesStatus: 'Pending', extracurricular: 'Arts Club' },
    { id: 'STU022', name: 'Victoria Young', class: 'Class 12', section: 'Section A', feesStatus: 'Paid', extracurricular: 'Football Team' },
    { id: 'STU023', name: 'William King', class: 'Class 9', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Music Band' },
    { id: 'STU024', name: 'Xander Wright', class: 'Class 11', section: 'Section A', feesStatus: 'Pending', extracurricular: 'Debate Club' },
    { id: 'STU025', name: 'Yara Lopez', class: 'Class 10', section: 'Section C', feesStatus: 'Paid', extracurricular: 'Science Club' }
  ]);

  // Filter students based on selected criteria
  const filteredStudents = students.filter(student =>
    (classFilter === 'All Classes' || student.class === classFilter) &&
    (sectionFilter === 'All Sections' || student.section === sectionFilter) &&
    (extracurricularFilter === 'Any' || student.extracurricular === extracurricularFilter) &&
    (feesStatusFilter === 'Any' || student.feesStatus === feesStatusFilter) &&
    (searchTerm === '' || student.name.toLowerCase().includes(searchTerm.toLowerCase()) || student.id.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort students
  const sortedStudents = [...filteredStudents].sort((a, b) => {
    let aValue = a[sortBy as keyof Student];
    let bValue = b[sortBy as keyof Student];
    
    if (typeof aValue === 'string') aValue = aValue.toLowerCase();
    if (typeof bValue === 'string') bValue = bValue.toLowerCase();
    
    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Pagination calculations
  const totalPages = Math.ceil(sortedStudents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedStudents = sortedStudents.slice(startIndex, endIndex);

  // Reset to first page when filters change
  const handleFilterChange = (filterSetter: (value: string) => void, value: string) => {
    filterSetter(value);
    setCurrentPage(1);
  };

  // Reset to first page when search changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Reset to first page when items per page changes
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const resetAllFilters = () => {
    setClassFilter('All Classes');
    setSectionFilter('All Sections');
    setExtracurricularFilter('Any Activity');
    setFeesStatusFilter('Any Status');
    setSearchTerm('');
    setCurrentPage(1);
  };

  const toggleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  return (
    <ComponentErrorBoundary componentName="Student List">
      <div className="space-y-3">
      {/* Compact Search and Filters Section */}
      <div className="bg-white rounded-xl shadow-xl shadow-slate-300/20 border border-slate-200/80 relative overflow-hidden mb-4">
        {/* Subtle top accent */}
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>

        <div className="relative z-10 p-3">
          {/* Compact Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full shadow-lg shadow-blue-500/30"></div>
              <h3 className="text-base font-bold text-slate-800">Search & Filter</h3>
              <span className="text-xs text-blue-700 bg-blue-50 px-2 py-0.5 rounded-full font-medium shadow-sm border border-blue-100">
                {filteredStudents.length} found
              </span>
            </div>
            <button
              onClick={resetAllFilters}
              className="group flex items-center space-x-2 bg-slate-800 hover:bg-slate-900 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm"
              title="Reset all filters"
            >
              <svg className="w-4 h-4 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Reset All</span>
            </button>
          </div>

            {/* Ultra-Compact Single Row Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-2 items-end">
              {/* Search Box - Takes 2 columns on xl screens */}
              <div className="xl:col-span-2">
                <label className="block text-xs font-medium text-slate-600 mb-1">
                  Search Students
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search by name or ID..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="w-full px-3 py-2 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-8 pr-7 bg-white shadow-lg shadow-blue-100/30 hover:shadow-xl hover:shadow-blue-100/40 transition-all duration-200 placeholder-slate-400"
                  />
                  <div className="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                    <svg className="h-3.5 w-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  {searchTerm && (
                    <div className="absolute inset-y-0 right-0 pr-1.5 flex items-center">
                      <button
                        onClick={() => handleSearchChange('')}
                        className="text-slate-400 hover:text-slate-600 transition-colors p-1 rounded-full hover:bg-slate-100"
                      >
                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Class Filter - 1 column */}
              <div className="group">
                <label className="block text-xs font-medium text-slate-600 mb-1 transition-colors group-focus-within:text-blue-600">
                  Class
                </label>
                <div className="relative">
                  <select
                    value={classFilter}
                    onChange={(e) => handleFilterChange(setClassFilter, e.target.value)}
                    className="w-full px-2.5 py-2 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg shadow-purple-100/40 hover:shadow-xl hover:shadow-purple-100/50 transition-all duration-200 appearance-none cursor-pointer"
                  >
                    <option>All Classes</option>
                    <option>Class 9</option>
                    <option>Class 10</option>
                    <option>Class 11</option>
                    <option>Class 12</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-1.5 flex items-center pointer-events-none">
                    <svg className="w-3.5 h-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Section Filter - 1 column */}
              <div className="group">
                <label className="block text-xs font-medium text-slate-600 mb-1 transition-colors group-focus-within:text-blue-600">
                  Section
                </label>
                <div className="relative">
                  <select
                    value={sectionFilter}
                    onChange={(e) => handleFilterChange(setSectionFilter, e.target.value)}
                    className="w-full px-2.5 py-2 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg shadow-emerald-100/40 hover:shadow-xl hover:shadow-emerald-100/50 transition-all duration-200 appearance-none cursor-pointer"
                  >
                    <option>All Sections</option>
                    <option>Section A</option>
                    <option>Section B</option>
                    <option>Section C</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-1.5 flex items-center pointer-events-none">
                    <svg className="w-3.5 h-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Activities Filter - 1 column */}
              <div className="group">
                <label className="block text-xs font-medium text-slate-600 mb-1 transition-colors group-focus-within:text-blue-600">
                  Activities
                </label>
                <div className="relative">
                  <select
                    value={extracurricularFilter}
                    onChange={(e) => handleFilterChange(setExtracurricularFilter, e.target.value)}
                    className="w-full px-2.5 py-2 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg shadow-orange-100/40 hover:shadow-xl hover:shadow-orange-100/50 transition-all duration-200 appearance-none cursor-pointer"
                  >
                    <option>Any Activity</option>
                    <option>Arts Club</option>
                    <option>Football Team</option>
                    <option>Music Band</option>
                    <option>Debate Club</option>
                    <option>Science Club</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-1.5 flex items-center pointer-events-none">
                    <svg className="w-3.5 h-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Fees Status Filter - 1 column */}
              <div className="group">
                <label className="block text-xs font-medium text-slate-600 mb-1 transition-colors group-focus-within:text-blue-600">
                  Fees Status
                </label>
                <div className="relative">
                  <select
                    value={feesStatusFilter}
                    onChange={(e) => handleFilterChange(setFeesStatusFilter, e.target.value)}
                    className="w-full px-2.5 py-2 border-2 border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-lg shadow-rose-100/40 hover:shadow-xl hover:shadow-rose-100/50 transition-all duration-200 appearance-none cursor-pointer"
                  >
                    <option>Any Status</option>
                    <option>Paid</option>
                    <option>Pending</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-1.5 flex items-center pointer-events-none">
                    <svg className="w-3.5 h-3.5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        {/* Top Border Highlight */}
        <div className="h-0.5 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>
        
        {/* Simple Table Header - No Navigation */}
        <div className="flex items-center justify-between px-6 py-4 bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full"></div>
            <h3 className="text-base font-semibold text-slate-800">Student Records</h3>
            <span className="text-xs text-slate-600 bg-slate-100 px-2 py-1 rounded-full font-medium border border-slate-200">
              {filteredStudents.length} of {students.length}
            </span>
          </div>
        </div>

        {/* Students Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-blue-100 text-blue-800">
              <tr className="border-b border-blue-200">
                <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider text-blue-700">
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" className="rounded border-blue-300 bg-blue-50 text-blue-600 focus:ring-blue-200" />
                    <button
                      onClick={() => toggleSort('name')}
                      className="flex items-center space-x-1 hover:text-blue-900 transition-colors"
                    >
                      <span>Student</span>
                      <div className="flex flex-col">
                        <svg className={`w-3 h-3 ${sortBy === 'name' && sortOrder === 'asc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                        <svg className={`w-3 h-3 -mt-1 ${sortBy === 'name' && sortOrder === 'desc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </button>
                  </div>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider text-blue-700">
                  <button
                    onClick={() => toggleSort('id')}
                    className="flex items-center space-x-1 hover:text-blue-900 transition-colors"
                  >
                    <span>Student ID</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'id' && sortOrder === 'asc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'id' && sortOrder === 'desc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider text-blue-700">
                  <button
                    onClick={() => toggleSort('class')}
                    className="flex items-center space-x-1 hover:text-blue-900 transition-colors"
                  >
                    <span>Class & Section</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'class' && sortOrder === 'asc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'class' && sortOrder === 'desc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider text-blue-700">
                  <button
                    onClick={() => toggleSort('feesStatus')}
                    className="flex items-center space-x-1 hover:text-blue-900 transition-colors"
                  >
                    <span>Fees Status</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'feesStatus' && sortOrder === 'asc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'feesStatus' && sortOrder === 'desc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium uppercase tracking-wider text-blue-700">
                  <button
                    onClick={() => toggleSort('extracurricular')}
                    className="flex items-center space-x-1 hover:text-blue-900 transition-colors"
                  >
                    <span>Activities</span>
                    <div className="flex flex-col">
                      <svg className={`w-3 h-3 ${sortBy === 'extracurricular' && sortOrder === 'asc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      <svg className={`w-3 h-3 -mt-1 ${sortBy === 'extracurricular' && sortOrder === 'desc' ? 'text-blue-800' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </button>
                </th>
                <th className="px-4 py-3 text-center text-sm font-medium uppercase tracking-wider text-blue-700">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedStudents.map((student) => (
                <tr key={student.id} className="table-row">
                  <td className="table-cell">
                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" />
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-base-app">
                          {student.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900 text-base-app">{student.name}</div>
                          <div className="text-sm-app text-gray-500">ID: {student.id}</div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="font-mono text-base-app text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {student.id}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="space-y-1">
                      <div className="text-base-app font-medium text-gray-900">{student.class}</div>
                      <div className="text-sm-app text-gray-500">{student.section}</div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-sm-app font-medium ${
                      student.feesStatus === 'Paid' 
                        ? 'bg-emerald-50 text-emerald-700 border border-emerald-200' 
                        : 'bg-red-50 text-red-700 border border-red-200'
                    }`}>
                      <div className={`w-1 h-1 rounded-full mr-1 ${
                        student.feesStatus === 'Paid' ? 'bg-emerald-500' : 'bg-red-500'
                      }`}></div>
                      {student.feesStatus}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="inline-flex items-center px-2 py-0.5 rounded-md text-sm-app font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      {student.extracurricular}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center justify-center space-x-1">
                      <button 
                        className="p-1.5 text-gray-500 hover:text-indigo-600 hover:bg-indigo-50 rounded transition-colors duration-150"
                        title="View Details"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button 
                        className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors duration-150"
                        title="Edit Student"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button 
                        className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded transition-colors duration-150"
                        title="Remove Student"
                      >
                        <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Empty State */}
          {paginatedStudents.length === 0 && (
            <div className="text-center py-12 bg-white">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">No students found</h3>
              <p className="text-sm text-gray-600 mb-4">
                {students.length === 0
                  ? "No students have been added yet. Start by adding your first student."
                  : "Try adjusting your search criteria or filters to find students."
                }
              </p>
              <button
                onClick={onAddNewStudent}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg text-sm font-medium transition-colors shadow-sm flex items-center space-x-2 mx-auto"
              >
                {students.length === 0 ? "Add First Student" : "Add New Student"}
              </button>
            </div>
          )}
        </div>

        {/* Pagination - Always visible, greyed out when no records */}
        <div className={`flex items-center justify-between px-4 py-2 bg-white border-t border-gray-200 ${
          filteredStudents.length === 0 ? 'opacity-50' : ''
        }`}>
          <div className="flex items-center text-sm-app text-gray-700">
            {filteredStudents.length > 0 ? (
              <>
                <span>Showing {startIndex + 1} to {Math.min(endIndex, filteredStudents.length)} of {filteredStudents.length} results</span>
                {filteredStudents.length !== students.length && (
                  <span className="text-gray-500 ml-2">(filtered from {students.length} total)</span>
                )}
              </>
            ) : (
              <span>Showing 0 to 0 of 0 results</span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 text-sm-app text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={currentPage === 1 || filteredStudents.length === 0}
              onClick={() => filteredStudents.length > 0 && setCurrentPage(Math.max(1, currentPage - 1))}
            >
              ← Previous
            </button>

            {(() => {
              if (filteredStudents.length === 0) {
                // Show just page 1 when no results
                return (
                  <button
                    className="px-3 py-1 text-sm-app rounded bg-blue-600 text-white cursor-default"
                    disabled
                  >
                    1
                  </button>
                );
              }

              // Simple pagination like attendance management
              const pages = [];
              const maxVisible = 5;
              
              if (totalPages <= maxVisible) {
                // Show all pages if total is small
                for (let i = 1; i <= totalPages; i++) {
                  pages.push(i);
                }
              } else {
                // Show first, some middle pages, and last
                pages.push(1);
                if (currentPage > 3) pages.push('...');
                
                // Show current page and neighbors
                const start = Math.max(2, currentPage - 1);
                const end = Math.min(totalPages - 1, currentPage + 1);
                for (let i = start; i <= end; i++) {
                  if (i !== 1 && i !== totalPages) {
                    pages.push(i);
                  }
                }
                
                if (currentPage < totalPages - 2) pages.push('...');
                if (totalPages > 1) pages.push(totalPages);
              }
              
              return pages.map((page, index) => (
                <button
                  key={index}
                  onClick={() => typeof page === 'number' && setCurrentPage(page)}
                  className={`px-3 py-1 text-sm-app rounded ${
                    page === currentPage
                      ? 'bg-blue-600 text-white'
                      : page === '...'
                      ? 'text-gray-400 cursor-default'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  disabled={page === '...'}
                >
                  {page}
                </button>
              ));
            })()}

            <button
              className="px-3 py-1 text-sm-app text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={currentPage === totalPages || filteredStudents.length === 0}
              onClick={() => filteredStudents.length > 0 && setCurrentPage(currentPage + 1)}
            >
              Next →
            </button>
          </div>
        </div>
      </div>
      </div>
    </ComponentErrorBoundary>
  );
};

export default StudentList;
