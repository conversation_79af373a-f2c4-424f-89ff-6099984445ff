// src/utils/index.ts
// Main entry point for all utilities

// Constants
export * from './constants';

// Helpers
export * from './helpers';

// Types
export * from './types';

// Legacy utilities (maintain backward compatibility)
export * from './auth';
export * from './auth-state-manager';
export * from './connection-handler';
export * from './metadata';

// Re-export commonly used items with convenient aliases
export {

    // API constants
    API_ROUTES as ApiRoutes,
    APP_ROUTES as AppRoutes, DATABASE_COLUMNS as Columns,
    // Database constants
    DATABASE_TABLES as Tables,
    // Validation
    VALIDATION_PATTERNS as ValidationPatterns
} from './constants';

export {

    // Formatters
    dateFormatters as formatDate, numberFormatters as formatNumber, textFormatters as formatText, codeGenerators as generateCode,
    // Generators
    idGenerators as generateId, masterDataTransformers as transformMasterData,
    // Transformers
    studentTransformers as transformStudent, basicValidators as validators
} from './helpers';

// Utility functions for common operations
export const utils = {
  // Quick access to commonly used functions
  formatDate: (date: string | Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  },
  
  formatName: (firstName: string, lastName: string) => {
    return `${firstName.trim()} ${lastName.trim()}`.trim();
  },
  
  generateId: () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },
  
  isValidEmail: (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim());
  },
  
  truncateText: (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength - 3) + '...';
  },
  
  capitalizeFirst: (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },
  
  sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },
  
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};
