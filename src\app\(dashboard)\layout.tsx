// src/app/(dashboard)/layout.tsx
import { Metadata } from 'next';
import { Suspense } from 'react';
import ErrorBoundary from '../../components/common/error-boundary';
import PageErrorBoundary from '../../components/common/page-error-boundary';
import { AppLayout } from '../../components/layout';
import { Loading } from '../../components/ui/loading';

// This layout wraps all dashboard routes with AppLayout
// Provides consistent sidebar, header, and footer for all management pages

export const metadata: Metadata = {
  title: 'Dashboard - EduPro',
  description: 'EduPro management dashboard with comprehensive school administration tools',
  keywords: 'dashboard, school management, education administration',
  authors: [{ name: 'EduPro Team' }],
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <Loading
          variant="branded"
          showProgress={true}
          size="md"
          fullScreen={true}
          message="Loading dashboard..."
        />
      }>
        <AppLayout>
          <PageErrorBoundary pageName="Dashboard Content">
            {children}
          </PageErrorBoundary>
        </AppLayout>
      </Suspense>
    </ErrorBoundary>
  );
}
