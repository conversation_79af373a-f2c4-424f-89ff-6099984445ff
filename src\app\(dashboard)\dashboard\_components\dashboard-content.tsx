// src/app/dashboard/_components/dashboard-content.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import AuthModal from '../../../../components/auth/auth-modal';
import { useAuth } from '../../../../components/auth/auth-provider';

const DashboardContent = () => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'staff' | 'events'>('overview');

  // Data for different tabs
  const getChartData = () => {
    switch (activeTab) {
      case 'overview':
        return {
          totalStudents: 1250,
          distribution: [
            { grade: 'Grade 9', count: 375, percentage: 30, color: '#3b82f6' },
            { grade: 'Grade 10', count: 313, percentage: 25, color: '#10b981' },
            { grade: 'Grade 11', count: 313, percentage: 25, color: '#8b5cf6' },
            { grade: 'Grade 12', count: 249, percentage: 20, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+12%',
          monthlyEnrollments: [45, 52, 48, 65, 72, 85],
          metrics: {
            attendance: 94,
            academic: 87,
            teacherSat: 91,
            parentEng: 78
          }
        };
      case 'staff':
        return {
          totalStudents: 85,
          distribution: [
            { grade: 'Teachers', count: 45, percentage: 53, color: '#3b82f6' },
            { grade: 'Admin Staff', count: 15, percentage: 18, color: '#10b981' },
            { grade: 'Support Staff', count: 20, percentage: 23, color: '#8b5cf6' },
            { grade: 'Part-time', count: 5, percentage: 6, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+8%',
          monthlyEnrollments: [2, 3, 1, 4, 2, 3],
          metrics: {
            attendance: 96,
            academic: 92,
            teacherSat: 88,
            parentEng: 85
          }
        };
      case 'events':
        return {
          totalStudents: 24,
          distribution: [
            { grade: 'Academic', count: 8, percentage: 33, color: '#3b82f6' },
            { grade: 'Sports', count: 6, percentage: 25, color: '#10b981' },
            { grade: 'Cultural', count: 7, percentage: 29, color: '#8b5cf6' },
            { grade: 'Community', count: 3, percentage: 13, color: '#f59e0b' }
          ],
          enrollmentGrowth: '+25%',
          monthlyEnrollments: [3, 4, 2, 5, 6, 4],
          metrics: {
            attendance: 89,
            academic: 95,
            teacherSat: 93,
            parentEng: 82
          }
        };
      default:
        return {
          totalStudents: 1250,
          distribution: [],
          enrollmentGrowth: '+12%',
          monthlyEnrollments: [],
          metrics: { attendance: 0, academic: 0, teacherSat: 0, parentEng: 0 }
        };
    }
  };

  const chartData = getChartData();

  // Handle successful login - redirect to dashboard
  const _handleAuthSuccess = () => {
    setIsAuthModalOpen(false);
    // The useEffect will handle the redirect once user state updates
  };

  useEffect(() => {
    // Only redirect if auth is fully loaded and user is definitely not authenticated
    // Remove aggressive timeout redirects that can interrupt valid sessions
    if (!loading && (!user || !user.isAuthenticated)) {
      // Add a longer delay before redirecting to allow for session restoration
      const timeoutId = setTimeout(() => {
        // Double-check auth state before redirecting
        if (!user || !user.isAuthenticated) {
          router.push('/product');
        }
      }, 10000); // Increased to 10 seconds

      return () => clearTimeout(timeoutId);
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>

          {/* Provide escape route */}
          <div className="mt-6">
            <button
              onClick={() => router.push('/product')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Product Page →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !user.isAuthenticated) {

    return (
      <>
        {/* Authentication Required Page - Product Theme */}
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
            <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="flex-grow flex items-center justify-center relative z-10 px-4">
            <div className="text-center max-w-2xl mx-auto">
              {/* Lock Icon */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full mb-8 shadow-2xl shadow-indigo-500/25">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V9a4 4 0 00-8 0v2m8 0V9a4 4 0 00-4-4 4 4 0 00-4 4v2" />
                </svg>
              </div>

              {/* Heading */}
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Authentication Required
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Please sign in to access your EduPro dashboard and manage your school operations.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button
                  onClick={() => setIsAuthModalOpen(true)}
                  className="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-indigo-500/25 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Sign In to Dashboard
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={() => router.push('/product')}
                  className="group px-8 py-4 border-2 border-slate-300 text-slate-700 rounded-2xl font-semibold text-lg hover:border-indigo-500 hover:text-indigo-600 hover:bg-indigo-50 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl"
                >
                  <span className="flex items-center justify-center">
                    Return Home
                    <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  </span>
                </button>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">Secure Access</div>
                    <div className="text-xs text-slate-600">Enterprise security</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">FERPA Compliant</div>
                    <div className="text-xs text-slate-600">Data protection</div>
                  </div>
                </div>

                <div className="flex items-center justify-center p-4 bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg">
                  <svg className="w-8 h-8 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="text-left">
                    <div className="font-semibold text-slate-800 text-sm">24/7 Support</div>
                    <div className="text-xs text-slate-600">Expert assistance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auth Modal */}
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          defaultTab="login"
        />
      </>
    );
  }

  return (
    <>
      {/* EduPro v2.0 Announcement Banner - Compact */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-lg p-2.5 mb-3 text-white shadow-lg shadow-indigo-200/40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="bg-white/20 rounded-md p-1">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-sm font-semibold">🎉 EduPro v2.0 is Live!</h3>
              <p className="text-blue-100 text-sm opacity-90">
                Discover enhanced features, blazing speed, and a refreshed interface
              </p>
            </div>
          </div>
          <button className="bg-white text-indigo-600 px-3 py-1.5 rounded-md text-sm font-semibold hover:bg-blue-50 transition-colors shadow-md shadow-white/20">
            Explore +
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-3">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-3">
          {/* Enroll Student */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Enroll Student</h3>
            <p className="text-sm text-gray-600 mb-auto">Quickly add new students to the school roster.</p>
            <button
              onClick={() => router.push('/student-management')}
              className="w-full bg-blue-200 text-blue-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-blue-300 transition-colors shadow-sm mt-2"
            >
              Enroll Now →
            </button>
          </div>

          {/* Manage Timetable */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-green-500/20 hover:shadow-xl hover:shadow-green-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Manage Timetable</h3>
            <p className="text-sm text-gray-600 mb-auto">View, create, or update class and exam schedules.</p>
            <button
              onClick={() => router.push('/academic-management')}
              className="w-full bg-green-200 text-green-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-green-300 transition-colors shadow-sm mt-2"
            >
              Open Timetable →
            </button>
          </div>

          {/* View Reports */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-orange-500/20 hover:shadow-xl hover:shadow-orange-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">View Reports</h3>
            <p className="text-sm text-gray-600 mb-auto">Access academic, attendance, or financial reports.</p>
            <button
              onClick={() => router.push('/reports')}
              className="w-full bg-orange-200 text-orange-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-orange-300 transition-colors shadow-sm mt-2"
            >
              Generate Reports →
            </button>
          </div>

          {/* Get Support */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-purple-500/20 hover:shadow-xl hover:shadow-purple-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Get Support</h3>
            <p className="text-sm text-gray-600 mb-auto">Find help documentation, FAQs, or contact support.</p>
            <button
              onClick={() => router.push('/resources')}
              className="w-full bg-purple-200 text-purple-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-purple-300 transition-colors shadow-sm mt-2"
            >
              Access Help →
            </button>
          </div>

          {/* Manage Fees */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-pink-500/20 hover:shadow-xl hover:shadow-pink-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Manage Fees</h3>
            <p className="text-sm text-gray-600 mb-auto">Handle fee collection, payments, and financial records.</p>
            <button
              onClick={() => router.push('/financial-management')}
              className="w-full bg-pink-200 text-pink-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-pink-300 transition-colors shadow-sm mt-2"
            >
              Manage Fees →
            </button>
          </div>

          {/* Library System */}
          <div className="bg-white rounded-lg p-3 shadow-lg shadow-indigo-500/20 hover:shadow-xl hover:shadow-indigo-600/30 transition-all duration-300 border border-gray-200 hover:-translate-y-1 flex flex-col">
            <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mb-2">
              <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1 text-sm">Library System</h3>
            <p className="text-sm text-gray-600 mb-auto">Manage books, issue records, and library resources.</p>
            <button
              onClick={() => router.push('/library-management')}
              className="w-full bg-indigo-200 text-indigo-800 py-1.5 px-3 rounded-md text-sm font-medium hover:bg-indigo-300 transition-colors shadow-sm mt-2"
            >
              Open Library →
            </button>
          </div>
        </div>
      </div>

      {/* School Analytics Dashboard - Modern Professional Redesign */}
      <div className="mb-3 bg-gradient-to-br from-slate-50/80 via-blue-50/40 to-indigo-50/60 rounded-2xl p-5 shadow-2xl shadow-blue-500/10 border border-white/60 backdrop-blur-lg relative overflow-hidden">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full -translate-x-20 -translate-y-20 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-full translate-x-16 translate-y-16 animate-pulse" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full -translate-x-12 -translate-y-12 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        {/* Modern Header with Enhanced Icons */}
        <div className="flex items-center justify-between mb-6 relative z-10">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/30">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 tracking-tight">School Analytics</h2>
                <p className="text-sm text-gray-600">Real-time insights & performance metrics</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full font-medium border border-emerald-200">Live Data</span>
            </div>
          </div>

          {/* Enhanced Toggle Controls */}
          <div className="flex bg-white/90 backdrop-blur-sm rounded-2xl p-1.5 shadow-lg shadow-slate-200/50 border border-white/60">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex items-center space-x-2 px-4 py-2.5 text-sm rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'overview'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg shadow-blue-500/30 transform scale-105'
                  : 'text-gray-600 hover:bg-white/80 hover:text-gray-800'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <span>Overview</span>
            </button>
            <button
              onClick={() => setActiveTab('staff')}
              className={`flex items-center space-x-2 px-4 py-2.5 text-sm rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'staff'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg shadow-blue-500/30 transform scale-105'
                  : 'text-gray-600 hover:bg-white/80 hover:text-gray-800'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <span>Staff</span>
            </button>
            <button
              onClick={() => setActiveTab('events')}
              className={`flex items-center space-x-2 px-4 py-2.5 text-sm rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'events'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg shadow-blue-500/30 transform scale-105'
                  : 'text-gray-600 hover:bg-white/80 hover:text-gray-800'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span>Events</span>
            </button>
          </div>
        </div>

        {/* Enhanced Dashboard Charts Section */}
        <div className="relative z-10 mb-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

            {/* Chart 1: Enhanced Distribution Donut Chart */}
            <div className="group bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/60 shadow-2xl shadow-blue-500/15 hover:shadow-3xl hover:shadow-blue-600/25 transition-all duration-500 hover:-translate-y-1">
              {/* Chart Header */}
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/30">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900">
                      {activeTab === 'overview' ? 'Student Distribution' :
                       activeTab === 'staff' ? 'Staff Distribution' : 'Event Categories'}
                    </h3>
                    <p className="text-xs text-gray-500">Real-time breakdown</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-2xl font-bold text-blue-600">{chartData.totalStudents}</span>
                  <p className="text-xs text-gray-500 font-medium">Total</p>
                </div>
              </div>

              {/* Enhanced Donut Chart */}
              <div className="relative w-44 h-44 mx-auto mb-6">
                <svg className="w-44 h-44 transform -rotate-90" viewBox="0 0 176 176">
                  {/* Background Circle with Gradient */}
                  <defs>
                    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#f1f5f9" />
                      <stop offset="100%" stopColor="#e2e8f0" />
                    </linearGradient>
                  </defs>
                  <circle cx="88" cy="88" r="65" fill="none" stroke="url(#bgGradient)" strokeWidth="16"/>

                  {/* Data Segments with Enhanced Styling */}
                  <circle cx="88" cy="88" r="65" fill="none" stroke="#3b82f6" strokeWidth="16"
                    strokeDasharray="122.5 408" strokeDashoffset="0"
                    className="transition-all duration-1000 drop-shadow-lg"
                    style={{filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))'}}/>

                  <circle cx="88" cy="88" r="65" fill="none" stroke="#10b981" strokeWidth="16"
                    strokeDasharray="102 408" strokeDashoffset="-122.5"
                    className="transition-all duration-1000 drop-shadow-lg"
                    style={{filter: 'drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3))'}}/>

                  <circle cx="88" cy="88" r="65" fill="none" stroke="#8b5cf6" strokeWidth="16"
                    strokeDasharray="102 408" strokeDashoffset="-224.5"
                    className="transition-all duration-1000 drop-shadow-lg"
                    style={{filter: 'drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3))'}}/>

                  <circle cx="88" cy="88" r="65" fill="none" stroke="#f59e0b" strokeWidth="16"
                    strokeDasharray="81.6 408" strokeDashoffset="-326.5"
                    className="transition-all duration-1000 drop-shadow-lg"
                    style={{filter: 'drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3))'}}/>
                </svg>

                {/* Enhanced Center Display */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center bg-white/90 backdrop-blur-sm rounded-full w-20 h-20 flex flex-col items-center justify-center shadow-lg">
                    <p className="text-xl font-bold text-gray-900">{chartData.totalStudents}</p>
                    <p className="text-xs text-gray-600 font-medium">
                      {activeTab === 'overview' ? 'Students' :
                       activeTab === 'staff' ? 'Staff' : 'Events'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Enhanced Legend */}
              <div className="space-y-3">
                {chartData.distribution.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-gray-50/50 hover:bg-gray-100/50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full shadow-sm"
                        style={{
                          backgroundColor: item.color,
                          boxShadow: `0 2px 4px ${item.color}40`
                        }}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">{item.grade}</span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-bold text-gray-900">{item.count}</span>
                      <span className="text-xs text-gray-500 ml-1">({item.percentage}%)</span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Enhanced Description */}
              <div className="mt-4 p-3 bg-blue-50/50 rounded-lg border border-blue-100/50">
                <p className="text-xs text-gray-600 leading-relaxed">
                  {activeTab === 'overview'
                    ? '📊 Current student distribution across grade levels. Strong Grade 9 enrollment indicates excellent admission strategies.'
                    : activeTab === 'staff'
                    ? '👥 Staff composition breakdown by role. Teacher majority ensures optimal student-teacher ratios for quality education.'
                    : '📅 Event category distribution. Balanced academic and cultural programming promotes comprehensive student development.'
                  }
                </p>
              </div>
            </div>

            {/* Chart 2: Enhanced Trends Line Chart */}
            <div className="group bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/60 shadow-2xl shadow-emerald-500/15 hover:shadow-3xl hover:shadow-emerald-600/25 transition-all duration-500 hover:-translate-y-1">
              {/* Chart Header */}
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center shadow-lg shadow-emerald-500/30">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900">
                      {activeTab === 'overview' ? 'Enrollment Trends' :
                       activeTab === 'staff' ? 'Staff Hiring Trends' : 'Event Frequency'}
                    </h3>
                    <p className="text-xs text-gray-500">6-month performance</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-emerald-600">{chartData.enrollmentGrowth}</span>
                  <p className="text-xs text-gray-500 font-medium">Growth</p>
                </div>
              </div>

              {/* Enhanced Line Chart */}
              <div className="relative h-40 mb-5 bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
                <svg className="w-full h-full" viewBox="0 0 320 160">
                  {/* Enhanced Grid */}
                  <defs>
                    <pattern id="enhancedGrid" width="53.33" height="32" patternUnits="userSpaceOnUse">
                      <path d="M 53.33 0 L 0 0 0 32" fill="none" stroke="#e2e8f0" strokeWidth="0.5" opacity="0.6"/>
                    </pattern>
                    <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stopColor="#10b981" stopOpacity="0.4"/>
                      <stop offset="50%" stopColor="#3b82f6" stopOpacity="0.2"/>
                      <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.1"/>
                    </linearGradient>
                    <filter id="glow">
                      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                      <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                      </feMerge>
                    </filter>
                  </defs>
                  <rect width="320" height="160" fill="url(#enhancedGrid)" />

                  {/* Enhanced Area Fill */}
                  <path d="M 30 110 L 85 95 L 140 88 L 195 70 L 250 55 L 290 40 L 290 160 L 30 160 Z"
                    fill="url(#trendGradient)" className="transition-all duration-1000"/>

                  {/* Enhanced Line with Glow */}
                  <path d="M 30 110 L 85 95 L 140 88 L 195 70 L 250 55 L 290 40"
                    fill="none" stroke="#10b981" strokeWidth="4" strokeLinecap="round"
                    className="transition-all duration-1000" filter="url(#glow)"/>

                  {/* Enhanced Data Points */}
                  <circle cx="30" cy="110" r="5" fill="#10b981" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 4px rgba(16, 185, 129, 0.4))'}}/>
                  <circle cx="85" cy="95" r="5" fill="#3b82f6" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.4))'}}/>
                  <circle cx="140" cy="88" r="5" fill="#8b5cf6" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 4px rgba(139, 92, 246, 0.4))'}}/>
                  <circle cx="195" cy="70" r="5" fill="#f59e0b" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 4px rgba(245, 158, 11, 0.4))'}}/>
                  <circle cx="250" cy="55" r="5" fill="#ef4444" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 4px rgba(239, 68, 68, 0.4))'}}/>
                  <circle cx="290" cy="40" r="6" fill="#10b981" className="transition-all duration-1000" style={{filter: 'drop-shadow(0 2px 6px rgba(16, 185, 129, 0.5))'}}/>
                </svg>
              </div>

              {/* Enhanced Month Labels */}
              <div className="flex justify-between text-xs font-medium text-gray-600 mb-4 px-2">
                <span className="bg-gray-100 px-2 py-1 rounded">Aug</span>
                <span className="bg-gray-100 px-2 py-1 rounded">Sep</span>
                <span className="bg-gray-100 px-2 py-1 rounded">Oct</span>
                <span className="bg-gray-100 px-2 py-1 rounded">Nov</span>
                <span className="bg-gray-100 px-2 py-1 rounded">Dec</span>
                <span className="bg-emerald-100 px-2 py-1 rounded text-emerald-700">Jan</span>
              </div>

              {/* Enhanced Key Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-emerald-50/50 rounded-xl border border-emerald-100/50">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <svg className="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    <span className="text-lg font-bold text-emerald-600">
                      +{chartData.monthlyEnrollments[chartData.monthlyEnrollments.length - 1]}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 font-medium">This Month</p>
                </div>
                <div className="text-center p-3 bg-blue-50/50 rounded-xl border border-blue-100/50">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className="text-lg font-bold text-blue-600">
                      {chartData.monthlyEnrollments.reduce((a, b) => a + b, 0)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 font-medium">6-Month Total</p>
                </div>
              </div>

              {/* Enhanced Description */}
              <div className="p-3 bg-emerald-50/50 rounded-lg border border-emerald-100/50">
                <p className="text-xs text-gray-600 leading-relaxed">
                  {activeTab === 'overview'
                    ? '📈 Consistent enrollment growth with January peak. Strong upward trajectory reflects excellent school reputation and increasing demand.'
                    : activeTab === 'staff'
                    ? '👥 Strategic hiring patterns align with growth. Steady recruitment maintains optimal staffing levels for quality education delivery.'
                    : '📊 Balanced event distribution throughout academic year. Peak periods strategically align with key academic calendar milestones.'
                  }
                </p>
              </div>
            </div>

            {/* Chart 3: Enhanced Performance Metrics Bar Chart */}
            <div className="group bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/60 shadow-2xl shadow-purple-500/15 hover:shadow-3xl hover:shadow-purple-600/25 transition-all duration-500 hover:-translate-y-1">
              {/* Chart Header */}
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center shadow-lg shadow-purple-500/30">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900">
                      {activeTab === 'overview' ? 'Key Metrics' :
                       activeTab === 'staff' ? 'Staff Performance' : 'Event Success'}
                    </h3>
                    <p className="text-xs text-gray-500">Performance indicators</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-purple-600">
                    {Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 90 ? 'Excellent' :
                     Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 80 ? 'Good' : 'Fair'}
                  </span>
                  <p className="text-xs text-gray-500 font-medium">Rating</p>
                </div>
              </div>

              {/* Enhanced Bar Chart Visualization */}
              <div className="space-y-5 mb-6">
                {/* Attendance Rate */}
                <div className="group/metric">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-emerald-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-gray-700">
                        {activeTab === 'overview' ? 'Attendance Rate' :
                         activeTab === 'staff' ? 'Staff Attendance' : 'Event Attendance'}
                      </span>
                    </div>
                    <span className="text-base font-bold text-emerald-600">{chartData.metrics.attendance}%</span>
                  </div>
                  <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-3 shadow-inner">
                    <div className="bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-600 h-3 rounded-full transition-all duration-1000 shadow-lg shadow-emerald-500/30 relative overflow-hidden"
                      style={{width: `${chartData.metrics.attendance}%`}}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Academic Performance */}
                <div className="group/metric">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-gray-700">
                        {activeTab === 'overview' ? 'Academic Performance' :
                         activeTab === 'staff' ? 'Performance Rating' : 'Educational Value'}
                      </span>
                    </div>
                    <span className="text-base font-bold text-blue-600">{chartData.metrics.academic}%</span>
                  </div>
                  <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-3 shadow-inner">
                    <div className="bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 h-3 rounded-full transition-all duration-1000 shadow-lg shadow-blue-500/30 relative overflow-hidden"
                      style={{width: `${chartData.metrics.academic}%`}}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Teacher Satisfaction */}
                <div className="group/metric">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-gray-700">
                        {activeTab === 'overview' ? 'Teacher Satisfaction' :
                         activeTab === 'staff' ? 'Job Satisfaction' : 'Organizer Rating'}
                      </span>
                    </div>
                    <span className="text-base font-bold text-purple-600">{chartData.metrics.teacherSat}%</span>
                  </div>
                  <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-3 shadow-inner">
                    <div className="bg-gradient-to-r from-purple-400 via-purple-500 to-purple-600 h-3 rounded-full transition-all duration-1000 shadow-lg shadow-purple-500/30 relative overflow-hidden"
                      style={{width: `${chartData.metrics.teacherSat}%`}}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Parent Engagement */}
                <div className="group/metric">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-amber-100 rounded-lg flex items-center justify-center">
                        <svg className="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-gray-700">
                        {activeTab === 'overview' ? 'Parent Engagement' :
                         activeTab === 'staff' ? 'Community Relations' : 'Parent Participation'}
                      </span>
                    </div>
                    <span className="text-base font-bold text-amber-600">{chartData.metrics.parentEng}%</span>
                  </div>
                  <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-3 shadow-inner">
                    <div className="bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 h-3 rounded-full transition-all duration-1000 shadow-lg shadow-amber-500/30 relative overflow-hidden"
                      style={{width: `${chartData.metrics.parentEng}%`}}>
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Summary Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-4 bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-xl border border-emerald-200/50 shadow-lg shadow-emerald-500/10">
                  <div className="flex items-center justify-center space-x-1 mb-2">
                    <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <span className="text-xl font-bold text-emerald-600">
                      {Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 90 ? 'A+' :
                       Math.round((chartData.metrics.attendance + chartData.metrics.academic + chartData.metrics.teacherSat + chartData.metrics.parentEng) / 4) >= 80 ? 'A' : 'B+'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Overall Grade</p>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200/50 shadow-lg shadow-blue-500/10">
                  <div className="flex items-center justify-center space-x-1 mb-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span className="text-xl font-bold text-blue-600">
                      {activeTab === 'overview' ? 'Top 5%' :
                       activeTab === 'staff' ? 'Excellent' : 'High Impact'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 font-medium">
                    {activeTab === 'overview' ? 'District Rank' :
                     activeTab === 'staff' ? 'Rating' : 'Success Rate'}
                  </p>
                </div>
              </div>

              {/* Enhanced Description */}
              <div className="p-3 bg-purple-50/50 rounded-lg border border-purple-100/50">
                <p className="text-xs text-gray-600 leading-relaxed">
                  {activeTab === 'overview'
                    ? '🎯 Comprehensive performance indicators across key areas. High attendance and academic scores reflect excellent school management and student engagement.'
                    : activeTab === 'staff'
                    ? '⭐ Staff performance metrics demonstrate high professional standards. Strong satisfaction scores indicate positive work environment and effective leadership.'
                    : '🏆 Event success metrics show excellent planning and execution. High participation rates and positive feedback reflect strong community engagement.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Footer with Additional Insights */}
        <div className="relative z-10 mt-6 p-4 bg-gradient-to-r from-blue-50/50 via-purple-50/30 to-indigo-50/50 rounded-xl border border-white/40">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg shadow-indigo-500/30">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h4 className="text-sm font-bold text-gray-900">Analytics Insights</h4>
                <p className="text-xs text-gray-600">
                  {activeTab === 'overview'
                    ? 'School performance trending upward with strong enrollment and engagement metrics'
                    : activeTab === 'staff'
                    ? 'Staff satisfaction and performance indicators show excellent workplace culture'
                    : 'Event success rates demonstrate strong community engagement and participation'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-right">
                <p className="text-xs text-gray-500">Last updated</p>
                <p className="text-xs font-medium text-gray-700">Just now</p>
              </div>
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DashboardContent;
