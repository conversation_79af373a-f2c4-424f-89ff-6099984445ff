// src/app/dashboard/page.tsx
'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import ErrorBoundary from '../../../components/common/error-boundary';
import { Loading } from '../../../components/ui/loading';
import DashboardContent from './_components/dashboard-content';

function DashboardPageContent() {
  const searchParams = useSearchParams();
  const [bypassAuth, setBypassAuth] = useState(false);

  useEffect(() => {
    // Check for bypass parameter
    const bypass = searchParams?.get('bypass') === 'true';
    setBypassAuth(bypass);
    console.log('Dashboard bypass mode:', bypass);
  }, [searchParams]);

  if (bypassAuth) {
    return (
      <div className="min-h-screen bg-gray-100 p-5">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-5">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Dashboard (Bypass Mode)</h1>
            <div className="p-3 bg-yellow-100 border border-yellow-300 rounded mb-4">
              <p className="text-yellow-800">⚠️ Running in bypass mode. Auth is disabled for debugging.</p>
            </div>
            <DashboardContent />
          </div>
        </div>
      </div>
    );
  }

  return (
    <DashboardContent />
  );
}

export default function DashboardPage() {
  console.log('DashboardPage rendering...');

  return (
    <ErrorBoundary>
      <Suspense fallback={
        <Loading 
          variant="branded"
          showProgress={true}
          size="md"
          fullScreen={true}
          message="Loading dashboard..."
        />
      }>
        <DashboardPageContent />
      </Suspense>
    </ErrorBoundary>
  );
}
