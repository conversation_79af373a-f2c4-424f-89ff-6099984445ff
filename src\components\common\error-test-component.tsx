'use client';

import { useState } from 'react';

/**
 * Error Testing Component for Development
 * Use this component to test error boundaries in development
 * Remove or disable in production
 */
export default function ErrorTestComponent() {
  const [shouldThrow, setShouldThrow] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const triggerError = () => {
    setShouldThrow(true);
  };

  const triggerAsyncError = async () => {
    // Simulate async error
    setTimeout(() => {
      throw new Error('Async error for testing error boundaries');
    }, 100);
  };

  const triggerRenderError = () => {
    // This will cause a render error
    setShouldThrow(true);
  };

  // This will throw during render if shouldThrow is true
  if (shouldThrow) {
    throw new Error('Test error thrown by ErrorTestComponent for testing error boundaries');
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-red-100 border border-red-300 rounded-lg p-4 shadow-lg">
      <div className="text-sm font-medium text-red-800 mb-3">
        🧪 Error Boundary Testing (Dev Only)
      </div>
      
      <div className="space-y-2">
        <button
          onClick={triggerRenderError}
          className="block w-full px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
        >
          Test Render Error
        </button>
        
        <button
          onClick={triggerAsyncError}
          className="block w-full px-3 py-1 bg-orange-600 text-white rounded text-xs hover:bg-orange-700 transition-colors"
        >
          Test Async Error
        </button>
        
        <button
          onClick={() => {
            // Test network error
            fetch('/api/nonexistent-endpoint')
              .catch(error => console.error('Network error:', error));
          }}
          className="block w-full px-3 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700 transition-colors"
        >
          Test Network Error
        </button>
      </div>
      
      <div className="text-xs text-red-600 mt-2">
        Use these buttons to test error boundaries
      </div>
    </div>
  );
}

/**
 * Hook to test error boundaries programmatically
 */
export function useErrorTest() {
  const throwError = (message: string = 'Test error') => {
    throw new Error(message);
  };

  const throwAsyncError = (message: string = 'Test async error') => {
    setTimeout(() => {
      throw new Error(message);
    }, 100);
  };

  return {
    throwError,
    throwAsyncError
  };
}
