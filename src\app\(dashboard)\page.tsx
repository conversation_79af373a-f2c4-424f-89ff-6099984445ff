// src/app/(dashboard)/page.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * Dashboard index page that redirects to the main dashboard
 * This handles the /dashboard route and redirects to /dashboard/dashboard
 */
export default function DashboardIndexPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main dashboard page
    router.replace('/dashboard/dashboard');
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Dashboard...</p>
      </div>
    </div>
  );
}
