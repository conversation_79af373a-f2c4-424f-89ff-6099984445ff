// src/components/layout/app-layout.tsx
'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../auth/auth-provider';
import { ComponentErrorBoundary } from '../common/page-error-boundary';
import AppContent from './app-content';
import AppFooter from './app-footer';
import AppHeader from './app-header';
import AppSidebar from './app-sidebar';

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
  showFooter?: boolean;
}

const AppLayout = ({ children, title, showFooter = false }: AppLayoutProps) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [redirectAttempted, setRedirectAttempted] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, loading } = useAuth();

  // Get current route for breadcrumb
  const getCurrentRoute = () => {
    const route = pathname.split('/')[1];
    return route || 'dashboard';
  };

  // Get page title based on route
  const getPageTitle = () => {
    if (title) return title;
    
    const routeTitles: Record<string, string> = {
      'dashboard': 'Dashboard',
      'student-management': 'Student Management',
      'staff-management': 'Staff Management',
      'academic-management': 'Academic Management',
      'attendance-management': 'Attendance Management',
      'fee-management': 'Fee Management',
    };

    const route = getCurrentRoute();
    return routeTitles[route] || 'Dashboard';
  };

  useEffect(() => {
    console.log('AppLayout auth state:', { user: !!user, loading, pathname, redirectAttempted });

    // Let middleware handle most authentication redirects
    // Only handle client-side redirects for specific edge cases
    if (!loading && !user && !redirectAttempted) {
      console.log('No user detected, checking if redirect is needed...');

      // Check if we're on a protected route that middleware might have missed
      const isProtectedRoute = [
        '/dashboard',
        '/student-management',
        '/staff-management',
        '/academic-management',
        '/attendance-management',
        '/fee-management',
        '/reports',
        '/settings'
      ].some(route => pathname.startsWith(route));

      if (isProtectedRoute) {
        // Much longer delay to allow for session restoration and prevent race conditions
        const timeoutId = setTimeout(() => {
          console.log('Protected route timeout expired, checking user state...');
          // Final check before redirecting
          if (!user && !redirectAttempted) {
            console.log('Redirecting to /auth from protected route');
            setRedirectAttempted(true);
            // Use window.location for hard redirect to avoid hydration issues
            window.location.href = '/auth';
          }
        }, 20000); // Increased to 20 seconds

        return () => {
          console.log('Cleaning up redirect timer');
          clearTimeout(timeoutId);
        };
      }
    }
  }, [user, loading, router, pathname, redirectAttempted]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="relative w-16 h-16 mx-auto mb-6">
            <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 border-r-cyan-400 animate-spin"></div>
            <div className="absolute inset-2 rounded-full border-2 border-transparent border-b-emerald-500 border-l-teal-400 animate-spin-reverse-slow"></div>
            <div className="absolute inset-4 bg-gradient-to-br from-blue-600 to-emerald-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">EP</span>
            </div>
          </div>
          <p className="text-gray-700 font-medium mb-2">Authenticating...</p>
          <p className="text-sm text-gray-500 mb-6">Verifying your session and loading dashboard</p>

          {/* Provide escape routes */}
          <div className="mt-6 space-y-3">
            <button
              onClick={() => window.location.href = '/dashboard?bypass=true'}
              className="block mx-auto px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Dashboard (Bypass Mode)
            </button>
            <button
              onClick={() => router.push('/auth')}
              className="block mx-auto px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium"
            >
              Go to Login
            </button>
            <button
              onClick={() => window.location.href = '/dashboard/simple'}
              className="block mx-auto px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-sm font-medium"
            >
              Simple Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to login...</p>
          <p className="text-sm text-gray-500 mt-2">Please wait while we redirect you</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100" style={{ overflow: 'visible' }}>
      <div className="flex min-h-screen">
        {/* Sidebar - Flexible height that grows with content */}
        <div className={`flex-shrink-0 transition-all duration-300 ${
          isSidebarCollapsed ? 'w-16' : 'w-72'
        }`} style={{ overflow: 'visible', position: 'relative', zIndex: 1000 }}>
          <ComponentErrorBoundary componentName="Sidebar">
            <AppSidebar
              isCollapsed={isSidebarCollapsed}
              onToggleCollapse={toggleSidebar}
            />
          </ComponentErrorBoundary>
        </div>

        {/* Main Content Area - Flexible height that grows with content */}
        <div className="flex flex-col flex-1 min-h-screen relative" style={{ marginLeft: '0', zIndex: 1 }}>
          {/* Header - Matches sidebar styling */}
          <div className="flex-shrink-0">
            <ComponentErrorBoundary componentName="Header">
              <AppHeader
                title={getPageTitle()}
                currentRoute={getCurrentRoute()}
              />
            </ComponentErrorBoundary>
          </div>

          {/* Page Content - Scrollable with footer space */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1">
              <AppContent>
                {children}
              </AppContent>
            </div>

            {/* Footer - Optional */}
            {showFooter && (
              <div className="flex-shrink-0">
                <ComponentErrorBoundary componentName="Footer">
                  <AppFooter />
                </ComponentErrorBoundary>
              </div>
            )}

            {/* Educational Quote Footer - Matches sidebar dark theme */}
            <div className="flex-shrink-0 bg-gradient-to-r from-slate-800 via-gray-700 to-slate-800 backdrop-blur-md border-t border-slate-600/50 shadow-lg shadow-slate-900/20">
              <div className="px-5 py-1">
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-7 h-7 bg-slate-600/30 rounded-full flex items-center justify-center flex-shrink-0 border border-slate-500/20">
                    <svg className="w-3.5 h-3.5 text-slate-300" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                    </svg>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-slate-200 leading-relaxed">
                      "Education is the most powerful weapon which you can use to change the world."
                    </p>
                    <p className="text-xs text-slate-400 mt-0.5">
                      — Nelson Mandela
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
