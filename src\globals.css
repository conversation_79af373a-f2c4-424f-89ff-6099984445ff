@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import centralized layout theme management system */
@import './styles/layout.css';

/* =============================================================================
                        CUSTOM ANIMATIONS FOR LOADING COMPONENTS
============================================================================= */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 2s linear infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Glassmorphism utilities */
.glass-card {
  @apply bg-white/20 backdrop-blur-xl border border-white/30 shadow-2xl;
}

/* Enhanced border utilities */
.border-3 {
  border-width: 3px;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 1.25rem 1.25rem; /* Convert 20px to rem */
}

/* =============================================================================
                        MISSING TYPOGRAPHY CLASSES
============================================================================= */

/* Missing text size classes */
.text-xs-app {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm-app {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base-app {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg-app {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl-app {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl-app {
  font-size: 1.75rem;
  line-height: 2rem;
}

/* =============================================================================
                        EDUPRO DESIGN SYSTEM - GLOBALS.CSS
============================================================================= */

/*
This file contains the centralized design system for the EduPro application.
All components should use these standardized classes for consistency.

USAGE GUIDELINES:
1. Always use the standardized classes (e.g., btn-primary, form-input-md)
2. Avoid inline styles or custom CSS unless absolutely necessary
3. Use the responsive typography system (text-xs-app, text-sm-app, etc.)
4. Follow the established component hierarchy (card-sm, card-md, card-lg)

COMPONENT SIZES:
- Small (sm): Compact components for dense layouts
- Medium (md): Default size for most components
- Large (lg): Prominent components and headers

===============================================================================
*/

/* Global Typography - Professional Sans Font System */
@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  html {
    font-size: 16px; /* Base font size for rem calculations */
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-weight: 400;
    line-height: 1.4; /* Tighter line height for compactness */
    color: #1e293b; /* slate-800 */
    background-color: #f8fafc; /* slate-50 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Responsive Typography System - Increased by 5% for better readability */
  .text-xs-app {
    font-size: 0.7875rem; /* 12.6px - 5% increase from 12px */
    line-height: 1.05rem; /* 16.8px - 5% increase from 16px */
  }

  .text-sm-app {
    font-size: 0.91875rem; /* 14.7px - 5% increase from 14px */
    line-height: 1.3125rem; /* 21px - 5% increase from 20px */
  }

  .text-base-app {
    font-size: 1.05rem; /* 16.8px - 5% increase from 16px */
    line-height: 1.575rem; /* 25.2px - 5% increase from 24px */
  }

  .text-lg-app {
    font-size: 1.18125rem; /* 18.9px - 5% increase from 18px */
    line-height: 1.8375rem; /* 29.4px - 5% increase from 28px */
  }

  .text-xl-app {
    font-size: 1.3125rem; /* 21px - 5% increase from 20px */
    line-height: 1.8375rem; /* 29.4px - 5% increase from 28px */
  }

  .text-2xl-app {
    font-size: 1.575rem; /* 25.2px - 5% increase from 24px */
    line-height: 2.1rem; /* 33.6px - 5% increase from 32px */
  }

  /* Section Headers - Consistent sizing and typography */
  .section-header-sm {
    @apply text-lg-app font-semibold text-slate-900;
    margin-bottom: 0.75rem; /* Convert mb-3 to rem */
  }

  .section-header-md {
    @apply text-xl-app font-semibold text-slate-900;
    margin-bottom: 1rem; /* Convert mb-4 to rem */
  }

  .section-header-lg {
    @apply text-2xl-app font-bold text-slate-900;
    margin-bottom: 1.25rem; /* Convert mb-5 to rem */
  }

  /* Default section header - medium size */
  .section-header {
    @apply section-header-md;
  }

  /* Responsive form inputs for mobile */
  @media (max-width: 640px) {
    .form-input {
      @apply form-input-sm;
    }
  }

  .form-button {
    @apply btn-md;
  }

  .form-button-primary {
    @apply btn-primary;
  }

  .form-button-secondary {
    @apply btn-secondary;
  }

  /* Legacy Card components - Updated to use standardized system */
  .card {
    @apply card-md;
  }

  .card-title {
    @apply card-title-md;
  }

  .card-content {
    @apply text-base-app text-slate-700;
  }

  .card-subtitle {
    @apply text-sm-app text-slate-600;
  }

  /* Legacy Table components - Updated to use standardized system */
  .table-header {
    @apply table-header-cell;
  }

  .table-cell {
    @apply text-base-app text-slate-700;
  }
}

/* =============================================================================
                        BUTTON COMPONENTS - STANDARDIZED SYSTEM
============================================================================= */

/* Primary Button Variants */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply bg-slate-100 hover:bg-slate-200 text-slate-700 font-medium rounded-lg transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2;
  @apply border border-slate-300 hover:border-slate-400;
}

.btn-success {
  @apply bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2;
  @apply shadow-sm hover:shadow-md;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  @apply shadow-sm hover:shadow-md;
}

/* Button Sizes */
.btn-sm {
  @apply px-3 py-1.5 text-sm-app;
}

.btn-md {
  @apply px-4 py-2 text-base-app;
}

.btn-lg {
  @apply px-6 py-3 text-lg-app;
}

/* =============================================================================
                        FORM COMPONENTS - STANDARDIZED SYSTEM
============================================================================= */

/* Form Input Variants */
.form-input {
  @apply w-full rounded-lg border border-slate-300 bg-white px-3 py-2 text-base-app;
  @apply placeholder:text-slate-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20;
  @apply transition-all duration-200;
}

.form-input-sm {
  @apply px-3 py-1.5 text-sm-app;
}

.form-input-md {
  @apply px-3 py-2 text-base-app;
}

.form-input-lg {
  @apply px-4 py-3 text-lg-app;
}

.form-input-error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500/20;
}

.form-input-success {
  @apply border-emerald-300 focus:border-emerald-500 focus:ring-emerald-500/20;
}

/* Form Labels */
.form-label {
  @apply block text-sm-app font-medium text-slate-700 mb-1;
}

.form-label-required::after {
  content: ' *';
  @apply text-red-500;
}

/* Form Error Messages */
.form-error {
  @apply text-sm-app text-red-600 mt-1;
}

.form-success {
  @apply text-sm-app text-emerald-600 mt-1;
}

/* Form Groups */
.form-group {
  @apply mb-4;
}

.form-group-sm {
  @apply mb-3;
}

.form-group-lg {
  @apply mb-6;
}

/* =============================================================================
                        CARD COMPONENTS - STANDARDIZED SYSTEM
============================================================================= */

/* Card Base Styles */
.card-base {
  @apply bg-white rounded-lg border border-slate-200 shadow-sm;
}

/* Card Variants */
.card-sm {
  @apply card-base p-3;
}

.card-md {
  @apply card-base p-4;
}

.card-lg {
  @apply card-base p-6;
}

/* Card Headers */
.card-header {
  @apply border-b border-slate-200 pb-3 mb-4;
}

.card-header-sm {
  @apply border-b border-slate-200 pb-2 mb-3;
}

.card-header-lg {
  @apply border-b border-slate-200 pb-4 mb-6;
}

/* Card Titles */
.card-title-sm {
  @apply text-lg-app font-semibold text-slate-900;
}

.card-title-md {
  @apply text-xl-app font-semibold text-slate-900;
}

.card-title-lg {
  @apply text-2xl-app font-bold text-slate-900;
}

/* Card Footers */
.card-footer {
  @apply border-t border-slate-200 pt-3 mt-4;
}

.card-footer-sm {
  @apply border-t border-slate-200 pt-2 mt-3;
}

.card-footer-lg {
  @apply border-t border-slate-200 pt-4 mt-6;
}

/* =============================================================================
                        TABLE COMPONENTS - STANDARDIZED SYSTEM
============================================================================= */

/* Table Base */
.table-base {
  @apply w-full border-collapse;
}

.table-container {
  @apply overflow-x-auto rounded-lg border border-slate-200;
}

/* Table Headers */
.table-header-cell {
  @apply bg-slate-50 px-4 py-3 text-left text-sm-app font-semibold text-slate-900 border-b border-slate-200;
}

/* Table Cells */
.table-cell {
  @apply px-4 py-3 text-base-app text-slate-700 border-b border-slate-200;
}

.table-cell-sm {
  @apply px-3 py-2 text-sm-app;
}

.table-cell-lg {
  @apply px-6 py-4 text-lg-app;
}

/* Table Rows */
.table-row {
  @apply hover:bg-slate-50 transition-colors duration-150;
}

.table-row-striped:nth-child(even) {
  @apply bg-slate-25;
}

/* =============================================================================
                        MODAL COMPONENTS - STANDARDIZED SYSTEM
============================================================================= */

/* Modal Overlay */
.modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
}

/* Modal Container */
.modal-container {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
}

/* Modal Content */
.modal-content {
  @apply bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto;
}

.modal-content-sm {
  @apply max-w-sm;
}

.modal-content-md {
  @apply max-w-md;
}

.modal-content-lg {
  @apply max-w-2xl;
}

.modal-content-xl {
  @apply max-w-4xl;
}

/* Modal Header */
.modal-header {
  @apply px-6 py-4 border-b border-slate-200;
}

.modal-title {
  @apply text-xl-app font-semibold text-slate-900;
}

/* Modal Body */
.modal-body {
  @apply px-6 py-4;
}

/* Modal Footer */
.modal-footer {
  @apply px-6 py-4 border-t border-slate-200 flex justify-end space-x-3;
}
