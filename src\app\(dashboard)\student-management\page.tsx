// src/app/student-management/page.tsx
'use client';

import { useState } from 'react';
import StudentList from './_components/student-list';

export default function StudentManagementPage() {
  const [showAddStudent, setShowAddStudent] = useState(false);

  const handleAddNewStudent = () => {
    setShowAddStudent(true);
    // TODO: Implement add student modal or navigation
    console.log('Add new student clicked');
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export clicked');
  };

  return (
    <div className="space-y-2">
      {/* Hero Section with Gradient - Matching Reports Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-emerald-100 via-cyan-50 to-sky-100 rounded-lg p-3 shadow-lg border border-white/50 h-[20vh]">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-200/20 via-transparent to-sky-200/20"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-cyan-200/30 to-transparent rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-emerald-200/30 to-transparent rounded-full blur-xl"></div>
        
        {/* Action Buttons - Top Right */}
        <div className="absolute top-3 right-3 z-20 flex items-center space-x-3">
          <button
            onClick={handleAddNewStudent}
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-lg flex items-center space-x-2 backdrop-blur-sm"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            <span>Add Student</span>
          </button>
          <button 
            onClick={handleExport}
            className="bg-white/90 hover:bg-white text-emerald-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-lg flex items-center space-x-2 backdrop-blur-sm border border-emerald-200"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export</span>
          </button>
        </div>
        
        <div className="relative z-10 h-full flex flex-col justify-center">
          <div className="max-w-2xl">
            <div className="inline-flex items-center px-2 py-0.5 bg-white/70 backdrop-blur-sm rounded-full border border-emerald-200/50 mb-3">
              <div className="w-1 h-1 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
              <span className="text-xs font-medium text-emerald-800">Student Administration</span>
            </div>
            <div className="flex items-center space-x-4 mb-3">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-bold mb-1 bg-gradient-to-r from-emerald-800 via-cyan-700 to-sky-800 bg-clip-text text-transparent leading-tight">
                  Student Management
                </h1>
                <p className="text-sm text-slate-700 leading-relaxed">
                  Comprehensive student administration platform for managing enrollments, records, and academic progress across your institution.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <StudentList onAddNewStudent={handleAddNewStudent} onExport={handleExport} />
    </div>
  );
}
