// src/app/academic-management/page.tsx
'use client';

import { useState } from 'react';
import PageErrorBoundary from '../../../components/common/page-error-boundary';

export default function AcademicManagementPage() {
  const [_activeTab, setActiveTab] = useState('overview');

  return (
    <PageErrorBoundary pageName="Academic Management">
      <div className="space-y-2">
      {/* Academic Setup Progress - Stepper Design */}
      <div className="relative overflow-hidden bg-gradient-to-br from-emerald-100 via-cyan-50 to-sky-100 rounded-lg p-3 shadow-lg border border-white/50">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-200/20 via-transparent to-sky-200/20"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-cyan-200/30 to-transparent rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-emerald-200/30 to-transparent rounded-full blur-xl"></div>
        
        <div className="relative z-10">
          <div className="text-center mb-4">
             <h2 className="text-xl font-bold text-gray-900 mb-1">Academic Year Setup</h2>
            <p className="text-sm text-gray-600 max-w-2xl mx-auto">
              Complete these essential steps to configure your academic year successfully
            </p>
          </div>

          <div className="relative max-w-5xl mx-auto">
            {/* Progress Line Background */}
            <div className="absolute top-8 left-12 right-12 h-1 bg-gray-200 rounded-full"></div>
            {/* Progress Line Active */}
            <div className="absolute top-8 left-12 h-1 bg-gradient-to-r from-green-500 to-blue-500 rounded-full transition-all duration-700" style={{ width: '25%' }}></div>

            {/* Steps */}
            <div className="relative flex justify-between items-start px-6">
              {[
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  ),
                  title: 'Define Classes & Sections',
                  status: 'completed',
                  step: 1
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  ),
                  title: 'Allocate Subjects',
                  status: 'current',
                  step: 2
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  ),
                  title: 'Set Timetables',
                  status: 'pending',
                  step: 3
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 8h.01M9 16h.01" />
                    </svg>
                  ),
                  title: 'Lesson Planning',
                  status: 'pending',
                  step: 4
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  ),
                  title: 'Manage Assignments',
                  status: 'pending',
                  step: 5
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  ),
                  title: 'Conduct Examinations',
                  status: 'pending',
                  step: 6
                },
                {
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  ),
                  title: 'Generate Report Cards',
                  status: 'pending',
                  step: 7
                }
              ].map((item, index) => (
                <div key={index} className="flex flex-col items-center relative group cursor-pointer transform transition-all duration-300 hover:scale-105">
                  {/* Step Circle with Icon */}
                  <div className={`
                    relative z-10 w-16 h-16 rounded-full flex items-center justify-center mb-2 border-2 transition-all duration-300 shadow-lg
                    ${item.status === 'completed' 
                      ? 'bg-gradient-to-r from-green-500 to-green-600 border-green-500 text-white shadow-green-200' 
                      : item.status === 'current' 
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 border-blue-500 text-white shadow-blue-200 ring-4 ring-blue-200 ring-opacity-50' 
                      : 'bg-white border-gray-300 text-gray-400 hover:border-gray-400 shadow-gray-100'
                    }
                  `}>
                    {item.status === 'completed' ? (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : item.status === 'current' ? (
                      <div className="flex items-center justify-center">
                        {item.icon}
                        <div className="absolute inset-0 rounded-full border-2 border-white border-t-transparent animate-spin opacity-50"></div>
                      </div>
                    ) : (
                      item.icon
                    )}
                  </div>

                  {/* Step Number Badge */}
                  <div className={`
                    absolute -top-1 -right-1 z-20 w-6 h-6 rounded-full text-xs font-bold flex items-center justify-center
                    ${item.status === 'completed' 
                      ? 'bg-green-100 text-green-700 border-2 border-green-500' 
                      : item.status === 'current' 
                      ? 'bg-blue-100 text-blue-700 border-2 border-blue-500' 
                      : 'bg-gray-100 text-gray-500 border-2 border-gray-300'
                    }
                  `}>
                    {item.step}
                  </div>

                  {/* Step Label */}
                  <div className="text-center max-w-24">
                    <h3 className={`text-xs font-semibold leading-tight mb-1 ${
                      item.status === 'completed' ? 'text-green-700' :
                      item.status === 'current' ? 'text-blue-700' :
                      'text-gray-600'
                    }`}>{item.title}</h3>
                    
                    {/* Status Badge */}
                    <div className={`
                      inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                      ${item.status === 'completed' 
                        ? 'bg-green-100 text-green-700' 
                        : item.status === 'current' 
                        ? 'bg-blue-100 text-blue-700' 
                        : 'bg-gray-100 text-gray-500'
                      }
                    `}>
                      {item.status === 'completed' && (
                        <>
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          Complete
                        </>
                      )}
                      {item.status === 'current' && (
                        <>
                          <div className="w-2 h-2 bg-current rounded-full mr-1 animate-pulse"></div>
                          In Progress
                        </>
                      )}
                      {item.status === 'pending' && (
                        <>
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Pending
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Left Column - Academic Overview & Key Dates */}
        <div className="lg:col-span-2">
          {/* Combined Academic Year Overview & Key Dates */}
          <div className="bg-white rounded-xl p-4 border border-slate-200 shadow-lg shadow-purple-100/50">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-gray-900">Academic Overview & Key Dates</h2>
              <button className="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center gap-1.5">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Add New Year
              </button>
            </div>

            {/* Academic Year Cards */}
            <div className="flex gap-2 mb-3 max-w-2xl">
              {/* Current Academic Year Card */}
              <div className="bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-lg p-2.5 shadow-sm flex-1 min-w-0">
                <div className="flex items-center justify-center mb-1.5">
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="text-center">
                  <p className="text-xs opacity-90 mb-0.5">Current Academic Year</p>
                  <p className="text-sm font-bold">2023-24</p>
                </div>
              </div>

              {/* Active Courses */}
              <div className="bg-white rounded-lg p-2.5 border border-gray-200 shadow-sm flex-1 min-w-0">
                <div className="flex items-center justify-center mb-1.5">
                  <div className="w-3.5 h-3.5 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm font-bold text-gray-900">45</p>
                  <p className="text-xs text-gray-600">Active Courses</p>
                  <p className="text-xs text-green-600 mt-0.5">70% of total</p>
                </div>
              </div>

              {/* Upcoming Exams */}
              <div className="bg-white rounded-lg p-2.5 border border-gray-200 shadow-sm flex-1 min-w-0">
                <div className="flex items-center justify-center mb-1.5">
                  <div className="w-3.5 h-3.5 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm font-bold text-gray-900">12</p>
                  <p className="text-xs text-gray-600">Upcoming Exams</p>
                  <p className="text-xs text-yellow-600 mt-0.5">Next week</p>
                </div>
              </div>
            </div>

            {/* Key Dates Section */}
            <div>
              <h3 className="text-base font-semibold text-gray-900 mb-2">Key Dates & Deadlines</h3>
              <div className="space-y-1.5">
                {[
                  {
                    title: 'Mid-term Exam Application Deadline',
                    subtitle: 'Closes: October 15, 2023',
                    status: 'Urgent',
                    statusColor: 'bg-red-100 text-red-700',
                    icon: (
                      <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    )
                  },
                  {
                    title: 'New Semester Course Registration',
                    subtitle: 'Starts: November 01, 2023',
                    status: '',
                    statusColor: '',
                    icon: (
                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    )
                  },
                  {
                    title: 'Science Fair Submissions Open',
                    subtitle: 'Deadline: November 30, 2023',
                    status: '',
                    statusColor: '',
                    icon: (
                      <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    )
                  },
                  {
                    title: 'Annual Sports Day',
                    subtitle: 'Event Date: December 05, 2023',
                    status: '',
                    statusColor: '',
                    icon: (
                      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    )
                  }
                ].map((item, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-2.5 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-2.5">
                      <div className="flex-shrink-0 mt-0.5">
                        {item.icon}
                      </div>
                      <div className="flex-grow">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-gray-900 text-sm">{item.title}</h4>
                            <p className="text-sm text-gray-600 mt-0.5">{item.subtitle}</p>
                          </div>
                          {item.status && (
                            <span className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${item.statusColor}`}>
                              {item.status}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Recent Activities */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl border border-slate-200 shadow-lg shadow-green-100/50 p-3">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activities</h2>
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>

            <div className="space-y-1.5">
              {[
                {
                  title: 'New assignment "Physics Chapter 5" for Class 10A.',
                  subtitle: 'Created by Ms. Gupta',
                  time: '2 hours ago',
                  icon: (
                    <svg className="w-3.5 h-3.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  ),
                  bgColor: 'bg-blue-50'
                },
                {
                  title: 'Timetable updated for Senior Section.',
                  subtitle: 'Updated by Admin',
                  time: '1 day ago',
                  icon: (
                    <svg className="w-3.5 h-3.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  ),
                  bgColor: 'bg-green-50'
                },
                {
                  title: 'Results published for Mid-Term Science Olympiad.',
                  subtitle: 'Published by Examination',
                  time: '2 days ago',
                  icon: (
                    <svg className="w-3.5 h-3.5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  ),
                  bgColor: 'bg-purple-50'
                },
                {
                  title: 'Parent-Teacher meeting scheduled for Class 8.',
                  subtitle: 'Scheduled by Mr. John',
                  time: '3 days ago',
                  icon: (
                    <svg className="w-3.5 h-3.5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  ),
                  bgColor: 'bg-yellow-50'
                },
                {
                  title: 'New library books added: "Introduction to AI".',
                  subtitle: 'Added by Librarian',
                  time: '4 days ago',
                  icon: (
                    <svg className="w-3.5 h-3.5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  ),
                  bgColor: 'bg-indigo-50'
                }
              ].map((activity, index) => (
                <div key={index} className="flex items-start gap-2.5">
                  <div className={`flex-shrink-0 w-7 h-7 ${activity.bgColor} rounded-full flex items-center justify-center`}>
                    {activity.icon}
                  </div>
                  <div className="flex-grow min-w-0">
                    <p className="text-sm text-gray-900 font-medium leading-tight">{activity.title}</p>
                    <p className="text-xs text-gray-500 mt-0.5">{activity.time} by {activity.subtitle}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-3 pt-2.5 border-t border-gray-200">
              <button className="w-full bg-blue-50 text-blue-700 py-1.5 px-3 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors flex items-center justify-center gap-1.5">
                View All Activities
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageErrorBoundary>
  );
}
