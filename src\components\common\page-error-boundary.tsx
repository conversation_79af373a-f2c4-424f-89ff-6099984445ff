'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

interface PageErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface PageErrorBoundaryProps {
  children: React.ReactNode;
  pageName?: string;
  fallback?: React.ComponentType<{ error: Error; reset: () => void; pageName?: string }>;
}

/**
 * Page-level error boundary that isolates errors to the content area only
 * Preserves app shell (sidebar, header, footer) functionality
 */
class PageErrorBoundary extends React.Component<PageErrorBoundaryProps, PageErrorBoundaryState> {
  constructor(props: PageErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): Partial<PageErrorBoundaryState> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Page Error Boundary (${this.props.pageName || 'Unknown Page'}) caught an error:`, {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    });

    this.setState({ errorInfo });

    // In production, send error to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error monitoring service (e.g., Sentry, LogRocket)
      console.log('Production error logged for monitoring');
    }
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback || PageErrorFallback;
      return (
        <Fallback 
          error={this.state.error!} 
          reset={() => this.setState({ hasError: false, error: null, errorInfo: null })} 
          pageName={this.props.pageName}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Default page error fallback that shows error in content area only
 * Maintains app shell functionality
 */
function PageErrorFallback({ 
  error, 
  reset, 
  pageName = 'Page' 
}: { 
  error: Error; 
  reset: () => void; 
  pageName?: string;
}) {
  const router = useRouter();

  const handleRetry = () => {
    reset();
  };

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleRefreshPage = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-[60vh] flex items-center justify-center p-8">
      <div className="max-w-lg w-full bg-white rounded-xl shadow-lg border border-red-200 p-8 text-center">
        {/* Error Icon */}
        <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>

        {/* Error Title */}
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {pageName} Error
        </h2>

        {/* Error Description */}
        <p className="text-gray-600 mb-6 leading-relaxed">
          We encountered an issue while loading this page. The rest of the application is still working normally.
        </p>

        {/* Error Details (Collapsible) */}
        <details className="text-left mb-6 bg-gray-50 rounded-lg p-4">
          <summary className="cursor-pointer text-sm text-gray-700 hover:text-gray-900 font-medium">
            Technical Details
          </summary>
          <div className="mt-3 space-y-2">
            <div className="text-xs text-gray-600">
              <strong>Error:</strong> {error.message}
            </div>
            <div className="text-xs text-gray-600">
              <strong>Time:</strong> {new Date().toLocaleString()}
            </div>
            <div className="text-xs text-gray-600">
              <strong>Page:</strong> {pageName}
            </div>
          </div>
        </details>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleRetry}
            className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Try Again
          </button>
          
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={handleRefreshPage}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
            >
              Refresh Page
            </button>
            <button
              onClick={handleGoToDashboard}
              className="px-4 py-2 bg-emerald-100 text-emerald-700 rounded-lg hover:bg-emerald-200 transition-colors text-sm"
            >
              Go to Dashboard
            </button>
          </div>
        </div>

        {/* Help Text */}
        <p className="text-xs text-gray-500 mt-6">
          If this problem persists, please contact support or try refreshing the page.
        </p>
      </div>
    </div>
  );
}

export default PageErrorBoundary;

/**
 * Component-level error boundary for smaller sections
 */
export function ComponentErrorBoundary({ 
  children, 
  componentName = 'Component',
  fallback 
}: {
  children: React.ReactNode;
  componentName?: string;
  fallback?: React.ReactNode;
}) {
  return (
    <PageErrorBoundary 
      pageName={componentName}
      fallback={fallback ? () => <>{fallback}</> : undefined}
    >
      {children}
    </PageErrorBoundary>
  );
}

/**
 * Simple inline error display for component failures
 */
export function InlineError({ 
  error, 
  onRetry,
  componentName = 'Component'
}: {
  error: string;
  onRetry?: () => void;
  componentName?: string;
}) {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800">
            {componentName} Error
          </h3>
          <p className="text-sm text-red-700 mt-1">
            {error}
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
