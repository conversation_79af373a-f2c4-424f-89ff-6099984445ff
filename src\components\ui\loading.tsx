'use client';

import { useEffect, useState } from 'react';

interface LoadingProps {
  message?: string;
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'branded' | 'modern';
  fullScreen?: boolean;
  overlay?: boolean;
}

export const Loading = ({ 
  message = "Loading...", 
  showProgress = true, 
  size = 'md',
  variant = 'modern',
  fullScreen = true,
  overlay = false
}: LoadingProps) => {
  const [progress, setProgress] = useState(0);
  const [loadingPhase, setLoadingPhase] = useState(0);
  const [dots, setDots] = useState('');

  const loadingPhases = [
    { 
      message: 'Initializing EduPro', 
      color: 'from-indigo-500 to-purple-600',
      bgGradient: 'from-indigo-50 to-purple-50',
      accent: 'indigo'
    },
    { 
      message: 'Loading your workspace', 
      color: 'from-purple-500 to-pink-600',
      bgGradient: 'from-purple-50 to-pink-50',
      accent: 'purple'
    },
    { 
      message: 'Setting up dashboard', 
      color: 'from-pink-500 to-rose-600',
      bgGradient: 'from-pink-50 to-rose-50',
      accent: 'pink'
    },
    { 
      message: 'Preparing interface', 
      color: 'from-rose-500 to-red-600',
      bgGradient: 'from-rose-50 to-red-50',
      accent: 'rose'
    },
    { 
      message: 'Almost ready', 
      color: 'from-emerald-500 to-teal-600',
      bgGradient: 'from-emerald-50 to-teal-50',
      accent: 'emerald'
    }
  ];

  useEffect(() => {
    const progressTimer = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + Math.random() * 8 + 2, 100);
        const phaseIndex = Math.min(Math.floor((newProgress / 100) * loadingPhases.length), loadingPhases.length - 1);
        setLoadingPhase(phaseIndex);
        return newProgress;
      });
    }, 150);

    const dotsTimer = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);

    return () => {
      clearInterval(progressTimer);
      clearInterval(dotsTimer);
    };
  }, []);

  const currentPhase = loadingPhases[loadingPhase];
  const estimatedTime = Math.max(1, Math.ceil((100 - progress) / 10));

  // Modern variant (main loading screen)
  if (variant === 'modern') {
    const containerClass = fullScreen 
      ? "fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-slate-100"
      : "flex items-center justify-center min-h-[400px] bg-gradient-to-br from-slate-50 via-white to-slate-100 rounded-xl";

    return (
      <div className={containerClass}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="modernGrid" width="60" height="60" patternUnits="userSpaceOnUse">
                <path d="M 60 0 L 0 0 0 60" fill="none" stroke="currentColor" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#modernGrid)" className="text-slate-400"/>
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className={`absolute w-2 h-2 bg-gradient-to-r ${currentPhase.color} rounded-full opacity-30 animate-float`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${i * 0.5}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="relative z-10 text-center max-w-md mx-auto px-6">
          {/* Logo Section */}
          <div className="mb-8">
            <div className="relative mx-auto w-20 h-20 mb-4">
              {/* Main Logo Circle */}
              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${currentPhase.color} shadow-2xl flex items-center justify-center transform transition-all duration-1000`}>
                <span className="text-white font-bold text-2xl tracking-tight">EP</span>
              </div>
              
              {/* Rotating Rings */}
              <div className="absolute inset-0 rounded-2xl border-4 border-transparent border-t-indigo-500 border-r-purple-500 animate-spin-slow"></div>
              <div className="absolute inset-2 rounded-xl border-2 border-transparent border-b-pink-400 border-l-rose-400 animate-spin-reverse"></div>
            </div>
            
            {/* Brand Name */}
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
              EduPro
            </h1>
            <p className="text-slate-500 font-medium tracking-wide">Education Management System</p>
          </div>

          {/* Progress Section */}
          {showProgress && (
            <div className="mb-8">
              {/* Status Card */}
              <div className="bg-white/70 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/50 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 bg-gradient-to-r ${currentPhase.color} rounded-full animate-pulse`}></div>
                    <span className="text-slate-700 font-semibold">{currentPhase.message}{dots}</span>
                  </div>
                  <span className="text-slate-500 text-sm font-medium">{Math.round(progress)}%</span>
                </div>
                
                {/* Progress Bar */}
                <div className="relative h-3 bg-slate-200 rounded-full overflow-hidden">
                  <div 
                    className={`absolute inset-y-0 left-0 bg-gradient-to-r ${currentPhase.color} rounded-full transition-all duration-500 ease-out relative overflow-hidden`}
                    style={{ width: `${Math.min(progress, 100)}%` }}
                  >
                    {/* Shimmer Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
                  </div>
                </div>
                
                {/* Phase Indicators */}
                <div className="flex justify-between mt-4">
                  {loadingPhases.map((phase, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index <= loadingPhase 
                          ? `bg-gradient-to-r ${phase.color}` 
                          : 'bg-slate-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Status Info */}
              <div className="flex items-center justify-center space-x-6 text-sm text-slate-500">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-ping"></div>
                  <span>Phase {loadingPhase + 1} of {loadingPhases.length}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>~{estimatedTime}s remaining</span>
                </div>
              </div>
            </div>
          )}

          {/* Inspirational Message */}
          <div className="text-center">
            <p className="text-slate-600 text-sm italic mb-2">
              "Empowering education through technology"
            </p>
            <div className="flex items-center justify-center space-x-1">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1.5 h-1.5 bg-gradient-to-r ${currentPhase.color} rounded-full animate-bounce`}
                  style={{ animationDelay: `${i * 0.2}s` }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Branded variant (alternative style)
  if (variant === 'branded') {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center z-50">
        <div className="text-center space-y-8 max-w-sm mx-auto px-6">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl mx-auto mb-6">
              <span className="text-white font-bold text-3xl">EP</span>
            </div>
            
            <h1 className="text-3xl font-bold text-slate-800 mb-2">EduPro</h1>
            <p className="text-slate-500">Loading your workspace...</p>
          </div>
          
          <div className="space-y-4">
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div 
                className="h-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
            <p className="text-sm text-slate-600">{Math.round(progress)}% complete</p>
          </div>
        </div>
      </div>
    );
  }

  // Minimal variant
  if (variant === 'minimal') {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center space-x-3">
          <div className="relative w-6 h-6">
            <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-indigo-500 animate-spin"></div>
          </div>
          <span className="text-sm font-medium text-slate-600">{message}</span>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className="flex flex-col items-center justify-center min-h-[300px] space-y-4">
      <div className="relative w-12 h-12">
        <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-indigo-500 border-r-purple-300 animate-spin"></div>
      </div>
      <p className="text-slate-600 font-medium">{message}</p>
    </div>
  );
};

export default Loading;
