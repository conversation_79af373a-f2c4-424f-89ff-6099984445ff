// src/lib/supabase/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { decrypt } from './session';

// Define protected and public routes
const protectedRoutes = [
  '/dashboard',
  '/student-management',
  '/staff-management',
  '/academic-management',
  '/attendance-management',
  '/fee-management',
  '/reports',
  '/settings'
];

const publicRoutes = [
  '/',
  '/product',
  '/resources',
  '/auth'
];

export default async function middleware(req: NextRequest) {
  // Get the pathname from the request URL
  const path = req.nextUrl.pathname;

  console.log(`[MIDDLEWARE] Processing request for path: ${path}`);

  // Skip middleware for static files, API routes, and assets to prevent ECONNRESET
  if (
    path.startsWith('/_next/') ||
    path.startsWith('/api/') ||
    path.includes('.') ||
    path.startsWith('/favicon') ||
    path.startsWith('/images/') ||
    path.startsWith('/icons/')
  ) {
    return NextResponse.next();
  }

  // Check if the current route is protected or public
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route));
  const isPublicRoute = publicRoutes.some(route => path === route || path.startsWith(route + '/'));

  // Get session from cookie with enhanced error handling
  let session = null;
  let sessionError = false;

  try {
    const cookie = req.cookies.get('session')?.value;
    if (cookie) {
      session = await decrypt(cookie);
      console.log(`[MIDDLEWARE] Session check for ${path}:`, !!session?.userId);
    } else {
      console.log(`[MIDDLEWARE] No session cookie found for ${path}`);
    }
  } catch (error) {
    console.error(`[MIDDLEWARE] Session decrypt error for ${path}:`, error);
    sessionError = true;
  }

  // For protected routes without valid session, redirect to auth
  if (isProtectedRoute && !session?.userId) {
    console.log(`[MIDDLEWARE] Redirecting ${path} to /auth - no valid session`);
    const response = NextResponse.redirect(new URL('/auth', req.nextUrl));

    // Clear any invalid session cookies
    if (sessionError) {
      response.cookies.delete('session');
    }

    // Add headers to prevent caching of redirects
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  }

  // Redirect authenticated users away from auth page
  if (path === '/auth' && session?.userId) {
    console.log(`[MIDDLEWARE] Redirecting /auth to /dashboard - user authenticated`);
    return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
  }

  // Enhanced session expiration check with better error handling
  if (session?.expiresAt) {
    try {
      const expirationTime = new Date(session.expiresAt);
      const currentTime = new Date();
      const gracePeriod = 10 * 60 * 1000; // Increased to 10 minutes grace period

      // Only redirect if session is expired beyond grace period
      if (currentTime > new Date(expirationTime.getTime() + gracePeriod)) {
        console.log(`[MIDDLEWARE] Session expired for ${path}, redirecting to /auth`);
        const response = NextResponse.redirect(new URL('/auth', req.nextUrl));
        response.cookies.delete('session');
        return response;
      }
    } catch (error) {
      console.error(`[MIDDLEWARE] Session expiration check error:`, error);
      // Continue without redirecting on date parsing errors
    }
  }

  console.log(`[MIDDLEWARE] Request allowed for ${path}`);
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)' 
  ],
};
