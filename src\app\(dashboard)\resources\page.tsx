'use client';

import { useState } from 'react';
import PageErrorBoundary from '../../../components/common/page-error-boundary';

export default function ResourcesPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Resources', count: 24 },
    { id: 'guides', name: 'User Guides', count: 8 },
    { id: 'videos', name: 'Video Tutorials', count: 6 },
    { id: 'templates', name: 'Templates', count: 5 },
    { id: 'best-practices', name: 'Best Practices', count: 5 }
  ];

  const resources = [
    {
      category: 'guides',
      title: 'Getting Started with EduPro',
      description: 'Complete setup guide for new administrators',
      type: 'PDF Guide',
      duration: '15 min read',
      featured: true,
      image: '📚'
    },
    {
      category: 'videos',
      title: 'Student Enrollment Process',
      description: 'Step-by-step video walkthrough',
      type: 'Video Tutorial',
      duration: '8 min watch',
      featured: true,
      image: '🎬'
    },
    {
      category: 'templates',
      title: 'Report Card Templates',
      description: 'Customizable templates for different grade levels',
      type: 'Template Pack',
      duration: '5 templates',
      featured: false,
      image: '📊'
    },
    {
      category: 'best-practices',
      title: 'Data Security Guidelines',
      description: 'Best practices for protecting student information',
      type: 'Security Guide',
      duration: '12 min read',
      featured: true,
      image: '🔒'
    },
    {
      category: 'guides',
      title: 'Grade Book Management',
      description: 'Comprehensive guide to managing grades and assessments',
      type: 'User Guide',
      duration: '20 min read',
      featured: false,
      image: '📝'
    },
    {
      category: 'videos',
      title: 'Parent Portal Setup',
      description: 'How to configure and customize the parent portal',
      type: 'Video Tutorial',
      duration: '12 min watch',
      featured: false,
      image: '👨‍👩‍👧‍👦'
    }
  ];

  const filteredResources = selectedCategory === 'all' 
    ? resources 
    : resources.filter(resource => resource.category === selectedCategory);

  return (
    <PageErrorBoundary pageName="Resources">
      <div className="p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Resources</h1>
          <p className="text-gray-600 dark:text-gray-400">Educational resources and documentation</p>
        </div>

        {/* Category Filter */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredResources.map((resource, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="text-2xl mb-2">{resource.image}</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{resource.title}</h3>
              <p className="text-gray-600 text-sm mb-2">{resource.description}</p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>{resource.type}</span>
                <span>{resource.duration}</span>
              </div>
              {resource.featured && (
                <div className="mt-2">
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    Featured
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      </div>
    </PageErrorBoundary>
  );
}
