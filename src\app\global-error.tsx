'use client';

import { useEffect } from 'react';

/**
 * Global error boundary for the entire Next.js application
 * This catches errors that escape all other error boundaries
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to console and external monitoring service
    console.error('Global Error Boundary caught an error:', {
      message: error.message,
      stack: error.stack,
      digest: error.digest,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    });

    // In production, send to error monitoring service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to error monitoring service (e.g., Sentry, LogRocket)
      console.log('Production error logged for monitoring');
    }
  }, [error]);

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoToDashboard = () => {
    window.location.href = '/dashboard';
  };

  const handleRefreshPage = () => {
    window.location.reload();
  };

  return (
    <html>
      <body>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-xl border border-red-200 p-8 text-center">
            {/* Error Icon */}
            <div className="w-20 h-20 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>

            {/* Error Title */}
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Application Error
            </h1>

            {/* Error Description */}
            <p className="text-gray-600 mb-6 leading-relaxed">
              We're sorry, but something went wrong with the application. Our team has been notified and is working to fix this issue.
            </p>

            {/* Error Details (Collapsible) */}
            <details className="text-left mb-6 bg-gray-50 rounded-lg p-4">
              <summary className="cursor-pointer text-sm text-gray-700 hover:text-gray-900 font-medium">
                Technical Details
              </summary>
              <div className="mt-3 space-y-2">
                <div className="text-xs text-gray-600">
                  <strong>Error:</strong> {error.message}
                </div>
                {error.digest && (
                  <div className="text-xs text-gray-600">
                    <strong>Error ID:</strong> {error.digest}
                  </div>
                )}
                <div className="text-xs text-gray-600">
                  <strong>Time:</strong> {new Date().toLocaleString()}
                </div>
              </div>
            </details>

            {/* Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={reset}
                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Try Again
              </button>
              
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleRefreshPage}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                >
                  Refresh Page
                </button>
                <button
                  onClick={handleGoToDashboard}
                  className="px-4 py-2 bg-emerald-100 text-emerald-700 rounded-lg hover:bg-emerald-200 transition-colors text-sm"
                >
                  Dashboard
                </button>
              </div>
              
              <button
                onClick={handleGoHome}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                Go to Home
              </button>
            </div>

            {/* Help Text */}
            <p className="text-xs text-gray-500 mt-6">
              If this problem persists, please contact our support team or try again later.
            </p>
          </div>
        </div>
      </body>
    </html>
  );
}
