// src/services/core/baseService.ts
import { Database } from '@/lib/supabase/types';
import { SupabaseClient } from '@supabase/supabase-js';
import { ErrorCode, ServiceError } from './errorHandler';

/**
 * Abstract base service class providing common functionality
 * for all service implementations following clean architecture principles
 */
export abstract class BaseService {
  protected client: SupabaseClient<Database>;

  constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  /**
   * Execute a database operation with error handling
   */
  protected async executeQuery<T>(
    operation: () => Promise<{ data: T | null; error: any }>,
    errorContext: string
  ): Promise<T> {
    try {
      const { data, error } = await operation();
      
      if (error) {
        throw new ServiceError(
          ErrorCode.DATABASE_ERROR,
          `${errorContext}: ${error.message}`,
          error
        );
      }

      if (data === null) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          `${errorContext}: No data returned`
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      
      throw new ServiceError(
        ErrorCode.UNKNOWN_ERROR,
        `${errorContext}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error
      );
    }
  }

  /**
   * Execute a database operation that may return null
   */
  protected async executeQueryOptional<T>(
    operation: () => Promise<{ data: T | null; error: any }>,
    errorContext: string
  ): Promise<T | null> {
    try {
      const { data, error } = await operation();
      
      if (error) {
        // Handle "not found" errors gracefully
        if (error.code === 'PGRST116') {
          return null;
        }
        
        throw new ServiceError(
          ErrorCode.DATABASE_ERROR,
          `${errorContext}: ${error.message}`,
          error
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ServiceError) {
        throw error;
      }
      
      throw new ServiceError(
        ErrorCode.UNKNOWN_ERROR,
        `${errorContext}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error
      );
    }
  }

  /**
   * Execute multiple operations in a transaction
   */
  protected async executeTransaction<T>(
    operations: (() => Promise<any>)[],
    errorContext: string
  ): Promise<T[]> {
    const results: T[] = [];
    
    try {
      // Note: Supabase doesn't support explicit transactions in the client
      // We'll implement a rollback strategy for failed operations
      for (const operation of operations) {
        const result = await operation();
        results.push(result);
      }
      
      return results;
    } catch (error) {
      // Attempt to rollback completed operations
      await this.rollbackOperations(results, errorContext);
      
      throw new ServiceError(
        ErrorCode.TRANSACTION_ERROR,
        `${errorContext}: Transaction failed`,
        error
      );
    }
  }

  /**
   * Rollback operations (to be implemented by subclasses)
   */
  protected async rollbackOperations(results: any[], context: string): Promise<void> {
    // Default implementation - log the rollback attempt
    console.warn(`Rollback attempted for ${context}:`, results);
  }

  /**
   * Validate required fields
   */
  protected validateRequired(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => 
      data[field] === undefined || data[field] === null || data[field] === ''
    );

    if (missingFields.length > 0) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        `Missing required fields: ${missingFields.join(', ')}`
      );
    }
  }

  /**
   * Sanitize input data
   */
  protected sanitizeInput(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = value.trim();
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * Generate unique identifier
   */
  protected generateId(): string {
    return crypto.randomUUID();
  }

  /**
   * Format date for database storage
   */
  protected formatDate(date: string | Date): string {
    if (typeof date === 'string') {
      return new Date(date).toISOString();
    }
    return date.toISOString();
  }

  /**
   * Check if user has permission for operation
   */
  protected async checkPermission(_operation: string, _resourceId?: string): Promise<boolean> {
    // TODO: Implement permission checking logic
    // For now, return true (implement based on your auth requirements)
    return true;
  }

  /**
   * Log service operation
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      service: this.constructor.name,
      level,
      message,
      data
    };

    switch (level) {
      case 'error':
        console.error(logEntry);
        break;
      case 'warn':
        console.warn(logEntry);
        break;
      default:
        console.log(logEntry);
    }
  }
}

/**
 * Service interface for dependency injection
 */
export interface IService {
  // Common service methods can be defined here
  readonly serviceName: string;
}

/**
 * Repository interface for data access abstraction
 */
export interface IRepository<T, TInsert = Partial<T>, TUpdate = Partial<T>> {
  findById(id: string): Promise<T | null>;
  findAll(filters?: Record<string, any>): Promise<T[]>;
  create(data: TInsert): Promise<T>;
  update(id: string, data: TUpdate): Promise<T>;
  delete(id: string): Promise<void>;
  exists(id: string): Promise<boolean>;
}

/**
 * Transaction interface for atomic operations
 */
export interface ITransaction {
  execute<T>(operations: (() => Promise<T>)[]): Promise<T[]>;
  rollback(): Promise<void>;
}
