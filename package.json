{"name": "edupro", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit", "seed:master-data": "npx tsx src/scripts/seed-master-data.ts"}, "dependencies": {"@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.8", "clsx": "^2.1.0", "framer-motion": "^12.19.2", "jose": "^6.0.11", "lucide-react": "^0.525.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "recharts": "^3.0.2", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.17.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.7.2"}}